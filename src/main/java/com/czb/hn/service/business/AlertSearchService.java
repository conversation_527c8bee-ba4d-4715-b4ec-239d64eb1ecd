package com.czb.hn.service.business;

import com.czb.hn.dto.alert.AlertSearchCriteriaDto;
import com.czb.hn.dto.alert.AlertSearchResultDto;
import com.czb.hn.dto.alert.AlertStatisticsDto;
import com.czb.hn.dto.workbench.AlertListItemDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Alert Search Service Interface
 * 预警信息搜索服务接口，提供多维度搜索和统计功能
 */
public interface AlertSearchService {

        /**
         * 综合搜索预警信息
         * 支持全文检索、多维度过滤和分页
         *
         * @param criteria 搜索条件
         * @return 搜索结果
         */
        AlertSearchResultDto searchAlerts(AlertSearchCriteriaDto criteria);

        /**
         * 获取指定时间段内指定方案的预警统计信息
         *
         * @param planId    方案ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 预警统计信息
         */
        AlertStatisticsDto getAlertStatistics(Long planId, LocalDateTime startTime, LocalDateTime endTime);

        /**
         * 获取指定方案的预警信息列表
         *
         * @param planId 方案ID
         * @param limit  返回记录数限制，默认20
         * @return 预警信息列表，按预警时间降序排列
         */
        List<AlertListItemDTO> getAlertListByPlanId(Long planId, Integer limit);

        /**
         * 获取指定方案在指定时间段内的预警信息列表
         *
         * @param planId    方案ID
         * @param startTime 开始时间（可选）
         * @param endTime   结束时间（可选）
         * @param limit     返回记录数限制，默认20
         * @return 预警信息列表，按预警时间降序排列
         */
        List<AlertListItemDTO> getAlertListByPlanIdAndTimeRange(Long planId, LocalDateTime startTime,
                        LocalDateTime endTime, Integer limit);

        /**
         * 获取企业下所有方案的预警信息列表
         *
         * @param enterpriseId 企业ID
         * @param limit        返回记录数限制，默认20
         * @return 预警信息列表，按预警时间降序排列
         */
        List<AlertListItemDTO> getAlertListByEnterpriseId(String enterpriseId, Integer limit);

        /**
         * 获取企业下所有方案在指定时间段内的预警信息列表
         *
         * @param enterpriseId 企业ID
         * @param startTime    开始时间（可选）
         * @param endTime      结束时间（可选）
         * @param limit        返回记录数限制，默认20
         * @return 预警信息列表，按预警时间降序排列
         */
        List<AlertListItemDTO> getAlertListByEnterpriseIdAndTimeRange(String enterpriseId, LocalDateTime startTime,
                        LocalDateTime endTime, Integer limit);

        /**
         * 获取多个方案在指定时间段内的预警信息列表
         *
         * @param planIds   方案ID列表
         * @param startTime 开始时间（可选）
         * @param endTime   结束时间（可选）
         * @param limit     返回记录数限制，默认20
         * @return 预警信息列表，按预警时间降序排列
         */
        List<AlertListItemDTO> getAlertListByPlanIdsAndTimeRange(List<Long> planIds, LocalDateTime startTime,
                        LocalDateTime endTime, Integer limit);
}
