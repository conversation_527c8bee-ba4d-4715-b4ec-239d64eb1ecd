package com.czb.hn.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.mail.BodyPart;
import javax.mail.MessagingException;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMultipart;
import javax.mail.util.ByteArrayDataSource;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 邮件模板工具类，用于生成邮件HTML内容和处理图片嵌入
 */
@Component
public class EmailTemplateUtil {
    private static final Logger log = LoggerFactory.getLogger(EmailTemplateUtil.class);

    /**
     * 生成简报邮件内容
     *
     * @param planName       方案名称
     * @param briefingType   简报类型（日报/周报/月报）
     * @param briefingPeriod 简报时间段
     * @param taskTime       任务时间
     * @param totalCount     监测内容总量
     * @param sensitiveRatio 敏感占比
     * @return 包含HTML内容和图片资源的MimeMultipart对象
     * @throws MessagingException 消息处理异常
     * @throws IOException        图片读取异常
     */
    public MimeMultipart createBulletinEmailContent(
            String planName,
            String briefingType,
            String briefingPeriod,
            String taskTime,
            String totalCount,
            String sensitiveRatio,
            byte[] pdfAttachment,
            String pdfFileName) throws MessagingException, IOException {

        // 创建HTML内容
        String htmlContent = String.format(
                "<div style=\"font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 15px;\">" +
                        "<p>主题：【%s】舆情监控%s已生成</p>" +
                        "<p>尊敬的用户：</p>" +
                        "<p>以下是%s方案%s的舆情汇总：</p>" +
                        "<ul style=\"list-style: none; padding-left: 0;\">" +
                        "<li style=\"margin: 10px 0;\"><img src=\"cid:icon_name\" style=\"width:18px;height:18px;vertical-align:middle;margin-right:8px;\"/>方案名称：%s</li>"
                        +
                        "<li style=\"margin: 10px 0;\"><img src=\"cid:icon_time\" style=\"width:18px;height:18px;vertical-align:middle;margin-right:8px;\"/>任务时间：%s</li>"
                        +
                        "<li style=\"margin: 10px 0;\"><img src=\"cid:icon_quantity\" style=\"width:18px;height:18px;vertical-align:middle;margin-right:8px;\"/>监测总量：%s</li>"
                        +
                        "<li style=\"margin: 10px 0;\"><img src=\"cid:icon_proportion\" style=\"width:18px;height:18px;vertical-align:middle;margin-right:8px;\"/>敏感占比：%s</li>"
                        +
                        "</ul>" +
                        "<p>更多相关信息请点击附件查看</p>" +
                        "</div>",
                planName, briefingType, planName, briefingPeriod, planName, taskTime, totalCount, sensitiveRatio);

        // 创建Multipart对象
        MimeMultipart multipart = new MimeMultipart("related");

        // 添加HTML内容
        MimeBodyPart htmlPart = new MimeBodyPart();
        htmlPart.setContent(htmlContent, "text/html;charset=UTF-8");
        multipart.addBodyPart(htmlPart);

        // 添加嵌入图片
        addInlineImage(multipart, "icon_name", "email/icon/icon_mail_name.png");
        addInlineImage(multipart, "icon_time", "email/icon/icon_mail_time.png");
        addInlineImage(multipart, "icon_quantity", "email/icon/icon_mail_quantity.png");
        addInlineImage(multipart, "icon_proportion", "email/icon/icon_mail_proportion.png");

        // 添加PDF附件
        if (pdfAttachment != null && pdfAttachment.length > 0) {
            MimeBodyPart attachmentPart = new MimeBodyPart();
            DataSource source = new ByteArrayDataSource(pdfAttachment, "application/pdf");
            attachmentPart.setDataHandler(new DataHandler(source));
            attachmentPart.setFileName(pdfFileName);
            attachmentPart.setDisposition(MimeBodyPart.ATTACHMENT);
            multipart.addBodyPart(attachmentPart);
        }

        return multipart;
    }

    /**
     * 添加内联图片到邮件内容
     */
    private void addInlineImage(MimeMultipart multipart, String contentId, String resourcePath)
            throws MessagingException, IOException {
        try {
            // 从classpath加载图片资源
            ClassPathResource resource = new ClassPathResource(resourcePath);
            byte[] imageBytes = Files.readAllBytes(Paths.get(resource.getURI()));

            // 创建图片部分
            MimeBodyPart imagePart = new MimeBodyPart();
            DataSource fds = new ByteArrayDataSource(imageBytes, "image/png");
            imagePart.setDataHandler(new DataHandler(fds));
            imagePart.setHeader("Content-ID", "<" + contentId + ">");
            imagePart.setDisposition(MimeBodyPart.INLINE);

            // 添加到multipart
            multipart.addBodyPart(imagePart);
        } catch (Exception e) {
            log.error("添加内联图片失败: {}", resourcePath, e);
            throw e;
        }
    }

    /**
     * 生成预警邮件内容
     *
     * @param planName       方案名称
     * @param alertTimeRange 预警时间段
     * @param alertCount     预警总量
     * @param sensitiveRatio 敏感占比
     * @param warningRatio   中等及严重预警占比（可选，为null时不显示）
     * @param detailUrl      查看详情链接
     * @return 包含HTML内容和图片资源的MimeMultipart对象
     * @throws MessagingException 消息处理异常
     * @throws IOException        图片读取异常
     */
    public MimeMultipart createAlertEmailContent(
            String planName,
            String alertTimeRange,
            int alertCount,
            String sensitiveRatio,
            String warningRatio,
            String detailUrl) throws MessagingException, IOException {

        // 创建HTML内容
        StringBuilder htmlBuilder = new StringBuilder();
        htmlBuilder.append("<div style=\"font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 15px;\">")
                .append("<p>主题：【").append(planName).append("】新增").append(alertCount).append("条预警</p>")
                .append("<p>尊敬的用户：</p>")
                .append("<p>以下是").append(planName).append("方案").append(alertTimeRange).append("的主要网络信息预警：</p>")
                .append("<ul style=\"list-style: none; padding-left: 0;\">")
                .append("<li style=\"margin: 10px 0;\"><img src=\"cid:icon_name\" style=\"width:18px;height:18px;vertical-align:middle;margin-right:8px;\"/>方案名称：")
                .append(planName).append("</li>")
                .append("<li style=\"margin: 10px 0;\"><img src=\"cid:icon_time\" style=\"width:18px;height:18px;vertical-align:middle;margin-right:8px;\"/>预警时间：")
                .append(alertTimeRange).append("</li>")
                .append("<li style=\"margin: 10px 0;\"><img src=\"cid:icon_quantity\" style=\"width:18px;height:18px;vertical-align:middle;margin-right:8px;\"/>预警总量：")
                .append(alertCount).append("</li>")
                .append("<li style=\"margin: 10px 0;\"><img src=\"cid:icon_proportion\" style=\"width:18px;height:18px;vertical-align:middle;margin-right:8px;\"/>敏感占比：")
                .append(sensitiveRatio).append("</li>");

        // 如果有中等及严重预警占比，则添加
        if (warningRatio != null && !warningRatio.trim().isEmpty()) {
            htmlBuilder.append(
                    "<li style=\"margin: 10px 0;\"><img src=\"cid:icon_warning\" style=\"width:18px;height:18px;vertical-align:middle;margin-right:8px;\"/>中等及严重预警占比：")
                    .append(warningRatio).append("</li>");
        }

        htmlBuilder.append("</ul>")
                .append("<p>更多相关信息请点击查看：<a href=\"").append(detailUrl)
                .append("\" style=\"color: #1890ff; text-decoration: none;\">查看详情</a></p>")
                .append("</div>");

        String htmlContent = htmlBuilder.toString();

        // 创建Multipart对象
        MimeMultipart multipart = new MimeMultipart("related");

        // 添加HTML内容
        MimeBodyPart htmlPart = new MimeBodyPart();
        htmlPart.setContent(htmlContent, "text/html;charset=UTF-8");
        multipart.addBodyPart(htmlPart);

        // 添加嵌入图片
        addInlineImage(multipart, "icon_name", "email/icon/icon_mail_name.png");
        addInlineImage(multipart, "icon_time", "email/icon/icon_mail_time.png");
        addInlineImage(multipart, "icon_quantity", "email/icon/icon_mail_quantity.png");
        addInlineImage(multipart, "icon_proportion", "email/icon/icon_mail_proportion.png");

        // 如果有中等及严重预警占比，则添加预警图标
        if (warningRatio != null && !warningRatio.trim().isEmpty()) {
            addInlineImage(multipart, "icon_warning", "email/icon/icon_mail_warning.png");
        }

        return multipart;
    }
}