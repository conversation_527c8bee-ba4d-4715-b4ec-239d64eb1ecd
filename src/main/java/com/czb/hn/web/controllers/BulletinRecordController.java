package com.czb.hn.web.controllers;

import com.czb.hn.constant.CommonConstants;
import com.czb.hn.dto.bulletin.BulletinGenerationRecordDto;
import com.czb.hn.dto.bulletin.BulletinPushRecordDto;
import com.czb.hn.dto.bulletin.ReceiveInfoBulletinPrepareDto;
import com.czb.hn.dto.common.PageResult;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.service.bulletin.BulletinRecordService;
import com.czb.hn.service.file.FileStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 简报记录控制器
 */
@RestController
@RequestMapping("/bulletin/records")
@Tag(name = "BulletinRecordController", description = "简报生成记录和推送记录的管理接口")
public class BulletinRecordController {
    
    private static final Logger log = LoggerFactory.getLogger(BulletinRecordController.class);
    
    private final BulletinRecordService bulletinRecordService;
    private final FileStorageService fileStorageService;


    public BulletinRecordController(BulletinRecordService bulletinRecordService, FileStorageService fileStorageService) {
        this.bulletinRecordService = bulletinRecordService;
        this.fileStorageService = fileStorageService;
    }

    /**
     * 根据方案ID查询简报生成记录列表
     *
     * @param planId       方案ID
     * @param startDate    开始日期（可选，yyyy-MM-dd）
     * @param endDate      结束日期（可选，yyyy-MM-dd）
     * @param bulletinType 简报类型（可选）
     * @param page         页码（从1开始）
     * @param size         每页大小
     * @return 简报生成记录分页结果
     */
    @GetMapping("/plan/{planId}")
    @Operation(summary = "根据方案ID查询简报生成记录", description = "根据方案ID、时间范围和/或简报类型查询简报生成记录，支持分页")
    @RolesAllowed(value = {CommonConstants.SYSTEM_ADMIN, CommonConstants.ENTERPRISE_ADMIN, CommonConstants.ENTERPRISE_USER})
    public ResponseEntity<ApiResponse<PageResult<BulletinGenerationRecordDto>>> searchGenerationRecordsByPlanId(
            @Parameter(description = "方案ID") @PathVariable Long planId,
            @Parameter(description = "开始日期，格式yyyy-MM-dd") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @Parameter(description = "结束日期，格式yyyy-MM-dd") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @Parameter(description = "简报类型（DAILY、WEEKLY、MONTHLY）") @RequestParam(required = false) String bulletinType,
            @Parameter(description = "页码，从1开始") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        try {
            // 将LocalDate转换为LocalDateTime
            LocalDateTime startTime = startDate != null ? startDate.atStartOfDay() : null;
            LocalDateTime endTime = endDate != null ? endDate.atTime(23, 59, 59) : null;
            PageResult<BulletinGenerationRecordDto> pageResult = bulletinRecordService.searchGenerationRecordsByPlanId(
                    planId,
                    startTime,
                    endTime,
                    bulletinType,
                    page - 1,
                    size);

            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success(pageResult));
        } catch (Exception e) {
            log.error("根据方案ID查询简报生成记录失败，方案ID: {}", planId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("查询简报生成记录失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据生成记录ID获取推送记录
     *
     * @param generationId 生成记录ID
     * @param page         页码（从1开始）
     * @param size         每页大小
     * @return 推送记录分页结果
     */
    @GetMapping("/{generationId}/pushes")
    @Operation(summary = "获取简报推送记录", description = "获取指定简报生成记录的所有推送记录，支持分页")
    @RolesAllowed(value = {CommonConstants.SYSTEM_ADMIN, CommonConstants.ENTERPRISE_ADMIN, CommonConstants.ENTERPRISE_USER})
    public ResponseEntity<ApiResponse<PageResult<BulletinPushRecordDto>>> getPushRecordsByGenerationId(
            @Parameter(description = "生成记录ID") @PathVariable Long generationId,
            @Parameter(description = "页码，从1开始") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        try {
            // 调用Service层获取分页DTO数据
            PageResult<BulletinPushRecordDto> pageResult = bulletinRecordService.getPushRecordsByGenerationId(
                generationId, 
                page - 1, 
                    size);
            
            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success(pageResult));
        } catch (Exception e) {
            log.error("获取推送记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取推送记录失败: " + e.getMessage()));
        }
    }

    @GetMapping("/{generationId}/receive-info")
    @Operation(summary = "获取简报接收人信息", description = "获取指定简报生成记录的接收人信息")
    public ResponseEntity<ApiResponse<ReceiveInfoBulletinPrepareDto>> getReceiveInfoBulletinPrepareDto(
            @Parameter(description = "生成记录ID") @PathVariable Long generationId) {
        return ResponseEntity.ok(ApiResponse.success(bulletinRecordService.getReceiveInfoBulletinPrepareDto(generationId)));
    }
    
    /**
     * 手动推送简报
     *
     * @param generationId 生成记录ID
     * @return 推送记录ID列表
     */
    @PostMapping("/{generationId}/push")
    @Operation(summary = "手动推送简报", description = "手动推送指定的简报到邮箱或短信")
    @RolesAllowed(value = {CommonConstants.SYSTEM_ADMIN, CommonConstants.ENTERPRISE_ADMIN, CommonConstants.ENTERPRISE_USER})
    public ResponseEntity<ApiResponse<List<Long>>> manualPushBulletin(
            @Parameter(description = "生成记录ID") @PathVariable Long generationId,
            @Parameter(description = "接收人信息") @RequestBody ReceiveInfoBulletinPrepareDto receiveInfoBulletinPrepareDto) {
        try {
            // 调用Service层执行推送
            List<Long> pushIds = bulletinRecordService.manualPushBulletin(generationId, receiveInfoBulletinPrepareDto);
            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success(pushIds));
        } catch (Exception e) {
            log.error("手动推送简报失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("手动推送简报失败: " + e.getMessage()));
        }
    }
    /**
     * 下载简报内容
     *
     * @param generationId 生成记录ID
     * @param response HTTP响应对象
     */
    @GetMapping("/{generationId}/download")
    @Operation(summary = "下载简报内容", description = "下载指定简报生成记录的PDF内容")
    @RolesAllowed(value = {CommonConstants.SYSTEM_ADMIN, CommonConstants.ENTERPRISE_ADMIN, CommonConstants.ENTERPRISE_USER})
    public void downloadBulletinContent(
            @Parameter(description = "生成记录ID") @PathVariable Long generationId,
            HttpServletResponse response) {
        try {
            // 获取文件信息
            BulletinRecordService.FileInfo fileInfo = bulletinRecordService.getBulletinFileInfo(generationId);

            // 获取文件内容
            byte[] content = bulletinRecordService.getBulletinContent(generationId);
            if (content == null || content.length == 0) {
                response.setStatus(HttpServletResponse.SC_NO_CONTENT);
                return;
            }

            // 设置响应头
            String fileName = URLEncoder.encode(fileInfo.fileName(), "UTF-8");
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            response.setContentLength(content.length);

            // 输出文件内容
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                outputStream.write(content);
                outputStream.flush();
            }
        } catch (Exception e) {
            log.error("下载简报内容失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取简报预览链接
     *
     * @param generationId 生成记录ID
     * @return 预览链接
     */
    @GetMapping("/{generationId}/preview-url")
    @Operation(summary = "获取简报预览链接", description = "获取指定简报生成记录的预览链接")
    public ApiResponse<String> getBulletinPreviewUrl(
            @Parameter(description = "生成记录ID") @PathVariable Long generationId) {
        try {
            // 获取文件信息
            BulletinRecordService.FileInfo fileInfo = bulletinRecordService.getBulletinFileInfo(generationId);

            // 获取预签名URL
            String previewUrl = fileStorageService.getPresignedUrl(fileInfo.fileId(), 3600); // 1小时有效期

            return ApiResponse.success(previewUrl);
        } catch (Exception e) {
            log.error("获取简报预览链接失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }


    /**
     * 删除简报生成记录（逻辑删除）
     * 
     * @param generationId 生成记录ID
     * @return 操作结果
     */
    @DeleteMapping("/{generationId}")
    @Operation(summary = "删除简报生成记录", description = "逻辑删除指定的简报生成记录")
    @RolesAllowed(value = {CommonConstants.SYSTEM_ADMIN, CommonConstants.ENTERPRISE_ADMIN})
    public ResponseEntity<ApiResponse<Void>> deleteGenerationRecord(@PathVariable Long generationId) {
        try {
            bulletinRecordService.deleteGenerationRecord(generationId);
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "删除成功", null));
        } catch (Exception e) {
            log.error("删除简报生成记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("删除简报生成记录失败: " + e.getMessage()));
        }
    }

    /**
     * 批量删除简报生成记录（逻辑删除）
     * 
     * @param ids 生成记录ID集合
     * @return 操作结果
     */
    @DeleteMapping("")
    @Operation(summary = "批量删除简报生成记录", description = "批量逻辑删除简报生成记录")
    @RolesAllowed(value = {CommonConstants.SYSTEM_ADMIN, CommonConstants.ENTERPRISE_ADMIN})
    public ResponseEntity<ApiResponse<Void>> deleteGenerationRecords(@RequestBody List<Long> ids) {
        try {
            bulletinRecordService.deleteGenerationRecords(ids);
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "批量删除成功", null));
        } catch (Exception e) {
            log.error("批量删除简报生成记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("批量删除简报生成记录失败: " + e.getMessage()));
        }
    }

    @PostMapping("/batch-download")
    @Operation(summary = "批量下载简报PDF", description = "批量下载指定生成记录的简报PDF，返回zip压缩包")
    @RolesAllowed(value = {CommonConstants.SYSTEM_ADMIN, CommonConstants.ENTERPRISE_ADMIN})
    public ResponseEntity<byte[]> batchDownloadBulletins(@RequestBody List<Long> ids) {
        try {
            byte[] zipBytes = bulletinRecordService.batchDownloadBulletins(ids);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "bulletins.zip");
            return new ResponseEntity<>(zipBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("批量下载简报PDF失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
} 